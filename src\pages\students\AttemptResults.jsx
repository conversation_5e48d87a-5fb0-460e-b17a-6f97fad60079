import { useEffect, useState, useContext } from 'react';
import { Box, Text, Button, useNavigate, useParams, useLocation } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import Loading from '../../components/utils/Loading';

import HeaderSpacer from '../../components/utils/HeaderSpacer';

const AttemptResults = () => {
    const { attemptId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [attempt, setAttempt] = useState(null);
    const [questions, setQuestions] = useState([]);
    const [activeTab, setActiveTab] = useState('all');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);



    // Lấy chi tiết kết quả
    useEffect(() => {
        if (!user || !attemptId) return;

        setLoading(true);
        authApi
            .get(`/exams/exam-attempts/${attemptId}`)
            .then((response) => {
                setAttempt(response.data.data);
                setQuestions(response.data.data.questions);
            })
            .catch((err) => {
                console.error('Error fetching attempt results:', err);
                setError('Lỗi khi tải kết quả');
            })
            .finally(() => setLoading(false));
    }, [user, attemptId]);

    // Định dạng ngày
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Định dạng thời gian làm bài
    const formatDuration = (startTime, endTime, timeLimit) => {
        const start = new Date(startTime).getTime();
        const end = new Date(endTime).getTime();
        const durationMs = end - start;
        const minutes = Math.floor(durationMs / 60000);
        return `${minutes} phút / ${timeLimit} phút`;
    };

    // Lọc câu hỏi theo tab
    const filteredQuestions = questions.filter((q) => {
        if (activeTab === 'all') return true;
        if (activeTab === 'correct') return q.isCorrect;
        if (activeTab === 'incorrect') return !q.isCorrect && q.selectedOption;
        if (activeTab === 'unanswered') return !q.selectedOption;
        return true;
    });

    // Tính thống kê
    const totalQuestions = questions.length;
    const correctCount = questions.filter((q) => q.isCorrect).length;
    const incorrectCount = questions.filter((q) => !q.isCorrect && q.selectedOption).length;
    const unansweredCount = questions.filter((q) => !q.selectedOption).length;
    const correctPercentage = (correctCount / totalQuestions) * 100;

    // Làm lại bài
    const handleRetry = async () => {
        try {
            const examId = attempt.exam._id;
            const response = await authApi.post(`/exams/${examId}/attempt`);
            const newAttemptId = response.data.data._id;
            navigate(`/attempt/${newAttemptId}`, {
                state: {
                    examId: examId, // Truyền examId vào state
                    previousPath: location.pathname
                }
            });
        } catch (err) {
            console.error('Error starting new attempt:', err);
            setError('Lỗi khi bắt đầu làm lại');
        }
    };

    if (error) {
        return <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{error}</Text>;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px' }}>
            <HeaderEdu
                title="Kết quả làm bài"
                showBackButton
                onBackClick={() => {
                    console.log('AttemptResults: handleBack called');
                    console.log('AttemptResults: attempt', attempt);
                    
                    // Lấy examId từ attempt
                    const examId = attempt?.exam?._id;
                    console.log('AttemptResults: examId', examId);
                    
                    if (examId) {
                        // Restore state for ExerciseDetail
                        const storedState = sessionStorage.getItem('exerciseDetailStateFromResults');
                        let exerciseDetailState = null;
                        
                        if (storedState) {
                            try {
                                exerciseDetailState = JSON.parse(storedState);
                                console.log('AttemptResults: Restored state for ExerciseDetail', exerciseDetailState);
                                // Clear after use
                                sessionStorage.removeItem('exerciseDetailStateFromResults');
                            } catch (e) {
                                console.error('Error parsing stored state:', e);
                            }
                        }
                        
                        // Quay về trang chi tiết bài tập với state được restore
                        console.log('AttemptResults: Navigating back to ExerciseDetail with state');
                        navigate(`/exercises/${examId}`, {
                            state: exerciseDetailState
                        });
                    } else {
                        // Nếu không có examId, quay về trang Exams
                        console.log('AttemptResults: No examId, fallback to /exams');
                        navigate('/exams', { replace: true });
                    }
                }}
            />
            <HeaderSpacer />

            {loading ? (<Loading />)
                : (
                    <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {/* Thẻ kết quả */}
                        <Box className="card">
                            <Box className="result-summary" style={{ textAlign: 'center', padding: '25px 15px', backgroundColor: '#f0f6ff', borderBottom: '1px solid #e0e0e0' }}>
                                <Text className="result-title" style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>
                                    {attempt?.exam.title}
                                </Text>
                                <Text className="result-subtitle" style={{ fontSize: '14px', color: '#666', marginBottom: '20px' }}>
                                    Hoàn thành lúc {formatDate(attempt?.attempt.endTime)}
                                </Text>
                                <Text className="score-display" style={{ fontSize: '48px', fontWeight: 'bold', color: '#0068ff', margin: '15px 0' }}>
                                    {attempt?.attempt.score.toFixed(1)}
                                </Text>
                                <Text className="score-text" style={{ fontSize: '16px', color: '#0068ff', marginBottom: '10px' }}>
                                    {correctCount}/{totalQuestions} câu đúng
                                </Text>
                            </Box>
                            <Box className="stats-container" style={{ display: 'flex', justifyContent: 'space-around', padding: '15px', borderBottom: '1px solid #eee' }}>
                                <Box className="stat-item" style={{ textAlign: 'center', padding: '0 10px' }}>
                                    <Text className="stat-value correct-value" style={{ fontSize: '20px', fontWeight: 'bold', color: '#28a745' }}>
                                        {correctCount}
                                    </Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                                        Đúng
                                    </Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center', padding: '0 10px' }}>
                                    <Text className="stat-value incorrect-value" style={{ fontSize: '20px', fontWeight: 'bold', color: '#dc3545' }}>
                                        {incorrectCount}
                                    </Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                                        Sai
                                    </Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center', padding: '0 10px' }}>
                                    <Text className="stat-value unanswered-value" style={{ fontSize: '20px', fontWeight: 'bold', color: '#6c757d' }}>
                                        {unansweredCount}
                                    </Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                                        Chưa trả lời
                                    </Text>
                                </Box>
                            </Box>
                            <Box className="time-info" style={{ padding: '15px', display: 'flex', justifyContent: 'space-between', borderBottom: '1px solid #eee', alignItems: 'center' }}>
                                <Text className="time-label" style={{ fontSize: '14px', color: '#666' }}>
                                    Thời gian làm bài:
                                </Text>
                                <Text className="time-value" style={{ fontWeight: '500', color: '#333' }}>
                                    {formatDuration(attempt?.attempt.startTime, attempt?.attempt.endTime, attempt?.exam.timeLimit)}
                                </Text>
                            </Box>
                            <Box className="action-buttons" style={{ display: 'flex', gap: '10px', padding: '15px' }}>
                                <Button className="btn btn-outline" style={{ flex: 1 }} onClick={handleRetry}>
                                    Làm lại
                                </Button>
                                {/* <Button className="btn btn-primary" style={{ flex: 1 }}>
                            Xem đáp án
                        </Button> */}
                            </Box>
                        </Box>

                        {/* Đánh giá kết quả */}
                        <Box className="result-message" style={{ backgroundColor: '#e8f0fe', padding: '15px', borderRadius: '8px', margin: '10px 15px', fontSize: '14px', color: '#0068ff', display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <Text className="result-message-icon" style={{ fontSize: '24px' }}>
                                {ICONS.DONE}
                            </Text>
                            <Text className="result-message-text" style={{ textAlign: 'left', flex: 1 }}>
                                {attempt?.score >= 8 ? 'Tuyệt vời! Bạn đã hoàn thành bài thi với số điểm cao.' : 'Tốt lắm! Hãy xem lại các câu sai để cải thiện kết quả nhé!'}
                            </Text>
                        </Box>

                        {/* Tab điều hướng */}
                        <Box className="tabs-container" style={{ display: 'flex', backgroundColor: '#f0f0f0', borderRadius: '8px', padding: '2px', margin: '15px' }}>
                            {[
                                { id: 'all', label: 'Tất cả câu hỏi' },
                                { id: 'correct', label: 'Câu đúng' },
                                { id: 'incorrect', label: 'Câu sai' },
                                { id: 'unanswered', label: 'Chưa trả lời' },
                            ].map((tab) => (
                                <Text
                                    key={tab.id}
                                    className={`tab ${activeTab === tab.id ? 'active' : ''}`}
                                    style={{
                                        flex: 1,
                                        textAlign: 'center',
                                        padding: '10px',
                                        fontSize: '14px',
                                        fontWeight: '500',
                                        cursor: 'pointer',
                                        borderRadius: '6px',
                                        backgroundColor: activeTab === tab.id ? '#0068ff' : 'transparent',
                                        color: activeTab === tab.id ? 'white' : '#333',
                                    }}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    {tab.label}
                                </Text>
                            ))}
                        </Box>

                        {/* Tổng quan tiến độ */}
                        <Box className="progress-container" style={{ padding: '0 15px 10px', display: 'flex', flexDirection: 'column', gap: '5px' }}>
                            <Box className="progress-label" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '13px', color: '#666' }}>
                                <Text>Tổng quan kết quả:</Text>
                                <Text>{totalQuestions} câu</Text>
                            </Box>
                            <Box className="progress-bar" style={{ height: '6px', backgroundColor: '#f0f0f0', borderRadius: '3px', overflow: 'hidden' }}>
                                <Box className="progress-fill progress-correct" style={{ height: '100%', borderRadius: '3px', width: `${correctPercentage}%` }} />
                            </Box>
                            <Box className="progress-label" style={{ display: 'flex', justifyContent: 'space-between', fontSize: '13px' }}>
                                <Text style={{ color: '#28a745' }}>Đúng: {correctCount} ({Math.round(correctPercentage)}%)</Text>
                                <Text style={{ color: '#dc3545' }}>Sai: {incorrectCount} ({Math.round((incorrectCount / totalQuestions) * 100)}%)</Text>
                                <Text style={{ color: '#6c757d' }}>Chưa trả lời: {unansweredCount} ({Math.round((unansweredCount / totalQuestions) * 100)}%)</Text>
                            </Box>
                        </Box>

                        {/* Danh sách câu hỏi */}
                        <Box className="tab-content active">
                            <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 15px 10px', color: '#333' }}>
                                Chi tiết các câu hỏi
                            </Text>
                            <Box className="question-list" style={{ padding: '0 15px 15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                                {filteredQuestions.map((question, index) => (
                                    <Box
                                        key={question._id}
                                        className={`question-item ${question.isCorrect ? 'correct' : question.selectedOption ? 'incorrect' : 'unanswered'}`}
                                        style={{
                                            backgroundColor: '#f8f9fa',
                                            borderRadius: '8px',
                                            padding: '15px',
                                            borderLeft: `4px solid ${question.isCorrect ? '#28a745' : question.selectedOption ? '#dc3545' : '#6c757d'}`,
                                        }}
                                    >
                                        <Box className="question-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
                                            <Text className="question-number" style={{ fontWeight: 'bold', color: '#333' }}>
                                                Câu {index + 1}
                                            </Text>
                                            <Text
                                                className={`question-status status-${question.isCorrect ? 'correct' : question.selectedOption ? 'incorrect' : 'unanswered'}`}
                                                style={{
                                                    fontSize: '12px',
                                                    padding: '2px 8px',
                                                    borderRadius: '12px',
                                                    backgroundColor: question.isCorrect ? '#d4edda' : question.selectedOption ? '#f8d7da' : '#e9ecef',
                                                    color: question.isCorrect ? '#28a745' : question.selectedOption ? '#dc3545' : '#6c757d',
                                                }}
                                            >
                                                {question.isCorrect ? 'Đúng' : question.selectedOption ? 'Sai' : 'Chưa trả lời'}
                                            </Text>
                                        </Box>
                                        <Text className="question-content" style={{ fontSize: '15px', marginBottom: '15px' }} dangerouslySetInnerHTML={{ __html: question.content }} />
                                        <Box className="options-container" style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '10px' }}>
                                            {question.options.map((option, index) => {
                                                // Sử dụng correctOption từ API để xác định đáp án đúng
                                                const isCorrectAnswer = index === question.correctOption;

                                                // Kiểm tra xem selectedOption có hợp lệ không
                                                const isSelected = question.selectedOption !== undefined &&
                                                                  question.selectedOption !== null &&
                                                                  question.selectedOption >= 0 &&
                                                                  question.selectedOption < question.options.length &&
                                                                  index === question.selectedOption;

                                                return (
                                                    <Box
                                                        key={option._id}
                                                        className={`option-item ${isCorrectAnswer ? 'option-correct' : ''
                                                            } ${isSelected ? 'option-selected' : ''}`}
                                                        style={{
                                                            padding: '10px',
                                                            borderRadius: '6px',
                                                            fontSize: '14px',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '10px',
                                                            backgroundColor:
                                                                isSelected && !question.isCorrect
                                                                    ? '#f8d7da' // Tô đỏ nếu chọn sai
                                                                    : isCorrectAnswer
                                                                        ? '#d4edda' // Tô xanh nếu là đáp án đúng
                                                                        : '#f0f0f0', // Màu mặc định
                                                        }}
                                                    >
                                                        <Box
                                                            className="option-marker"
                                                            style={{
                                                                minWidth: '28px',
                                                                height: 'auto',
                                                                borderRadius: '4px',
                                                                padding: '0 8px',
                                                                backgroundColor:
                                                                    isSelected && !question.isCorrect
                                                                        ? '#dc3545' // Đỏ cho đáp án sai
                                                                        : isCorrectAnswer
                                                                            ? '#28a745' // Xanh cho đáp án đúng
                                                                            : '#f0f0f0', // Mặc định
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                fontWeight: '500',
                                                                fontSize: '12px',
                                                                color:
                                                                    isSelected || isCorrectAnswer
                                                                        ? 'white'
                                                                        : '#666',
                                                            }}
                                                        >
                                                            {option.text} {/* Hiển thị text như 1, 2, 3, 4 */}
                                                        </Box>
                                                        <Text
                                                            className="option-content"
                                                            style={{
                                                                fontSize: '15px',
                                                                flex: 1, // Chiếm không gian còn lại
                                                                whiteSpace: 'normal', // Cho phép xuống dòng
                                                                wordWrap: 'break-word', // Đảm bảo text dài xuống dòng đúng
                                                                lineHeight: '1.5', // Tăng khoảng cách dòng
                                                            }}
                                                            dangerouslySetInnerHTML={{ __html: option.content }}
                                                        />
                                                    </Box>
                                                );
                                            })}
                                        </Box>
                                        <Box className="explanation-container" style={{ backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', fontSize: '14px', marginTop: '10px', borderLeft: '3px solid #0068ff' }}>
                                            <Text className="explanation-title" style={{ fontWeight: '500', color: '#0068ff', marginBottom: '6px' }}>
                                                Lời giải
                                            </Text>
                                            <Text className="explanation-content" style={{ color: '#444' }} dangerouslySetInnerHTML={{ __html: question.explanation || 'Không có lời giải' }} />
                                        </Box>
                                    </Box>
                                ))}
                            </Box>
                        </Box>
                    </Box>
                )}

            <BottomNavigationEdu active="exercises" />
        </Box>
    );
};

export default AttemptResults;