import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, List, useNavigate, Modal, Button, Input, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';

const ConductManagement = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const [loading, setLoading] = useState(true);
    const [contentLoading, setContentLoading] = useState(false);
    const [students, setStudents] = useState([]);
    const [classes, setClasses] = useState([]);
    const [selectedStudent, setSelectedStudent] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [adjustmentData, setAdjustmentData] = useState({
        points: '',
        reason: '',
        type: 'adjust'
    });
    const [submitting, setSubmitting] = useState(false);
    const [filters, setFilters] = useState({
        classId: '',
        search: ''
    });
    const [fetchError, setFetchError] = useState('');
    const [classStats, setClassStats] = useState(null);
    const { error, success } = useNotification();

    // Check user role access
    useEffect(() => {
        if (user && !user.role?.includes('teacher') && !user.role?.includes('admin')) {
            navigate('/student', { replace: true });
        }
    }, [user, navigate]);

    // Fetch initial data
    useEffect(() => {
        if (user && (user.role?.includes('teacher') || user.role?.includes('admin'))) {
            fetchInitialData().finally(() => setLoading(false));
        }
    }, [user]);

    // Fetch students when filters change
    useEffect(() => {
        if (user && (user.role?.includes('teacher') || user.role?.includes('admin'))) {
            fetchStudents();
        }
    }, [filters, user]);

    const fetchInitialData = async () => {
        try {
            // Get classes based on user role
            let endpoint = '/directory/classes';
            if (user?.role?.includes('teacher') && !user?.role?.includes('admin')) {
                // Teachers get their own classes
                endpoint = '/directory/teacher/classes';
            }
            
            const response = await authApi.get(endpoint);
            
            // Handle different response structures
            const classesData = response.data?.data || response.data || [];
            setClasses(Array.isArray(classesData) ? classesData : []);
        } catch (error) {
            console.error('Error fetching classes:', error);
            setClasses([]);
        }
    };

    const fetchStudents = async () => {
        try {
            setContentLoading(true);
            setFetchError('');
            
            // If no specific class selected, show message for user to select a class
            if (!filters.classId) {
                setStudents([]);
                setFetchError('Vui lòng chọn lớp để xem danh sách học sinh và điểm thi đua.');
                setContentLoading(false);
                return;
            }

            // First get list of students in the selected class
            const classResponse = await authApi.get(`/directory/class/${filters.classId}`);
            const classData = classResponse.data?.data || classResponse.data;
            const studentsInClass = classData?.students || [];

            if (studentsInClass.length === 0) {
                setStudents([]);
                setFetchError('Lớp này chưa có học sinh nào.');
                setContentLoading(false);
                return;
            }

            // Then get conduct points for each student
            const conductPromises = studentsInClass.map(async (student) => {
                try {
                    const response = await authApi.get(`/violations/conduct/${student.id}`, {
                        params: {
                            schoolYear: user?.schoolYear || '2024-2025'
                        }
                    });
                    
                    if (response.data?.success && response.data?.data) {
                        return {
                            ...response.data.data,
                            id: response.data.data.id || `conduct_${student.id}`,
                            classification: response.data.data.currentClassification || 'average'
                        };
                    } else {
                        // If no conduct data exists, create default entry
                        return {
                            id: `conduct_${student.id}`,
                            student: student,
                            class: classData,
                            schoolYear: user?.schoolYear || '2024-2025',
                            initialPoints: 50,
                            currentPoints: 50,
                            totalViolations: 0,
                            totalPointsDeducted: 0,
                            totalPointsAdded: 0,
                            classification: 'excellent',
                            pointHistory: [],
                            lastUpdated: new Date().toISOString()
                        };
                    }
                } catch (err) {
                    console.warn(`Error fetching conduct for student ${student.id}:`, err);
                    // Return default conduct data if API fails
                    return {
                        id: `conduct_${student.id}`,
                        student: student,
                        class: classData,
                        schoolYear: user?.schoolYear || '2024-2025',
                        initialPoints: 50,
                        currentPoints: 50,
                        totalViolations: 0,
                        totalPointsDeducted: 0,
                        totalPointsAdded: 0,
                        classification: 'excellent',
                        pointHistory: [],
                        lastUpdated: new Date().toISOString()
                    };
                }
            });

            const conductData = await Promise.all(conductPromises);
            
            // Filter by search term if provided
            let filteredData = conductData;
            if (filters.search?.trim()) {
                const searchTerm = filters.search.toLowerCase().trim();
                filteredData = conductData.filter(item => 
                    item.student?.name?.toLowerCase().includes(searchTerm) ||
                    item.student?.studentId?.toLowerCase().includes(searchTerm)
                );
            }

            setStudents(filteredData);
            
            // Also fetch class statistics
            await fetchClassStats();
        } catch (error) {
            console.error('Error fetching students:', error);
            setStudents([]);
            setFetchError('Không thể tải dữ liệu học sinh. Vui lòng thử lại sau.');
        } finally {
            setContentLoading(false);
        }
    };

    // Fetch class statistics
    const fetchClassStats = async () => {
        if (!filters.classId) {
            setClassStats(null);
            return;
        }

        try {
            const response = await authApi.get(`/violations/conduct/class/${filters.classId}/stats`, {
                params: {
                    schoolYear: user?.schoolYear || '2024-2025'
                }
            });
            
            if (response.data?.success) {
                setClassStats(response.data.data);
            }
        } catch (err) {
            console.warn('Error fetching class stats:', err);
            setClassStats(null);
        }
    };

    // Get classification info
    const getClassificationInfo = (classification) => {
        const classMap = {
            'excellent': { label: 'Xuất sắc', color: '#28a745', icon: '🏆' },
            'good': { label: 'Tốt', color: '#17a2b8', icon: '⭐' },
            'fair': { label: 'Khá', color: '#ffc107', icon: '👍' },
            'average': { label: 'Trung bình', color: '#fd7e14', icon: '👌' },
            'weak': { label: 'Yếu', color: '#dc3545', icon: '👎' }
        };
        return classMap[classification] || { label: classification, color: '#6c757d', icon: '❓' };
    };

    // Get points color
    const getPointsColor = (points) => {
        if (points >= 80) return '#28a745';
        if (points >= 65) return '#17a2b8';
        if (points >= 50) return '#ffc107';
        if (points >= 35) return '#fd7e14';
        return '#dc3545';
    };

    // Handle student selection
    const handleStudentSelect = (student) => {
        setSelectedStudent(student);
        setModalVisible(true);
        setAdjustmentData({
            points: '',
            reason: '',
            type: 'adjust'
        });
    };

    // Handle adjustment submission
    const handleAdjustmentSubmit = async () => {
        if (!adjustmentData.points || !adjustmentData.reason.trim()) {
            error('Vui lòng nhập đầy đủ thông tin');
            return;
        }

        if (adjustmentData.reason.trim().length < 5) {
            error('Lý do phải có ít nhất 5 ký tự');
            return;
        }

        const points = parseInt(adjustmentData.points);
        if (isNaN(points) || points <= 0) {
            error('Số điểm phải là số dương');
            return;
        }

        try {
            setSubmitting(true);
            
            const response = await authApi.put(`/violations/conduct/${selectedStudent.student.id}`, {
                schoolYear: user?.schoolYear || '2024-2025',
                points: points,
                reason: adjustmentData.reason.trim(),
                type: adjustmentData.type
            });
            
            if (response.data?.success) {
                setModalVisible(false);
                setSelectedStudent(null);
                fetchStudents(); // Refresh list
                success('Cập nhật điểm thi đua thành công');
            } else {
                error(response.data?.message || 'Có lỗi xảy ra khi cập nhật điểm thi đua');
            }
        } catch (err) {
            console.error('Error updating conduct points:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Có lỗi xảy ra khi cập nhật điểm thi đua';
            error(errorMessage);
        } finally {
            setSubmitting(false);
        }
    };

    // Handle filter change
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({ ...prev, [field]: value }));
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Quản lý điểm thi đua"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Filters */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '15px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                        {ICONS.FILTER} Bộ lọc
                    </Text>
                    
                    <Box style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '12px' }}>
                        <Select
                            placeholder="Tất cả lớp"
                            value={filters.classId}
                            onChange={(value) => handleFilterChange('classId', value)}
                        >
                            <Select.Option key="all-classes" value="" title="Tất cả lớp" />
                            {classes.map(cls => (
                                <Select.Option key={cls.id || cls.id} value={cls.id || cls.id} title={cls.name} />
                            ))}
                        </Select>

                        <Input
                            placeholder="Tìm kiếm học sinh..."
                            value={filters.search}
                            onChange={(e) => handleFilterChange('search', e.target.value)}
                        />
                    </Box>
                </Box>

                {/* Class Statistics */}
                {classStats && classStats.length > 0 && (
                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '15px',
                        marginBottom: '15px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                            {ICONS.STATS} Thống kê lớp
                        </Text>
                        
                        <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))', gap: '12px' }}>
                            {classStats.map((stat, index) => {
                                const classificationInfo = getClassificationInfo(stat.id);
                                return (
                                    <Box key={index} style={{
                                        textAlign: 'center',
                                        padding: '12px',
                                        borderRadius: '8px',
                                        backgroundColor: `${classificationInfo.color}10`,
                                        border: `1px solid ${classificationInfo.color}30`
                                    }}>
                                        <Text style={{ fontSize: '20px', marginBottom: '4px' }}>
                                            {classificationInfo.icon}
                                        </Text>
                                        <Text style={{ 
                                            fontSize: '18px', 
                                            fontWeight: 'bold', 
                                            color: classificationInfo.color,
                                            marginBottom: '2px'
                                        }}>
                                            {stat.count}
                                        </Text>
                                        <Text style={{ fontSize: '10px', color: '#666', marginBottom: '4px' }}>
                                            {classificationInfo.label}
                                        </Text>
                                        <Text style={{ fontSize: '9px', color: '#888' }}>
                                            TB: {stat.avgPoints?.toFixed(1) || 0}
                                        </Text>
                                    </Box>
                                );
                            })}
                        </Box>
                    </Box>
                )}

                {/* Error Message */}
                {fetchError && (
                    <Box style={{
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7',
                        borderRadius: '12px',
                        padding: '15px',
                        marginBottom: '15px',
                        textAlign: 'center'
                    }}>
                        <Text style={{ color: '#856404', fontSize: '14px' }}>
                            {ICONS.WARNING} {fetchError}
                        </Text>
                    </Box>
                )}

                {/* Students List */}
                <Box style={{ backgroundColor: 'white', borderRadius: '12px', padding: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold style={{ fontSize: '18px', color: '#333' }}>
                            {ICONS.STUDENT} Danh sách học sinh
                        </Text>
                        {students.length > 0 && (
                            <Text style={{ fontSize: '14px', color: '#666' }}>
                                {students.length} học sinh
                            </Text>
                        )}
                    </Box>

                    {contentLoading ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '40px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : students.length === 0 && !fetchError ? (
                        <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                            <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.INFO}</Text>
                            <Text bold style={{ fontSize: '18px', marginBottom: '10px' }}>
                                Không có học sinh nào
                            </Text>
                            <Text style={{ color: '#666', fontSize: '14px' }}>
                                {user?.role?.includes('admin') 
                                    ? 'Chưa có dữ liệu điểm thi đua hoặc chưa có học sinh nào phù hợp với bộ lọc đã chọn.'
                                    : 'Chưa có học sinh nào trong lớp của bạn có dữ liệu điểm thi đua.'
                                }
                            </Text>
                        </Box>
                    ) : (
                        <Box>
                            {students.map((item, index) => {
                                const classificationInfo = getClassificationInfo(item.classification);
                                return (
                                    <Box
                                        key={item.id || index}
                                        onClick={() => handleStudentSelect(item)}
                                        style={{
                                            padding: '15px',
                                            border: '1px solid #e0e0e0',
                                            borderRadius: '8px',
                                            marginBottom: '10px',
                                            backgroundColor: '#fff',
                                            cursor: 'pointer',
                                            transition: 'all 0.2s ease',
                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                                        }}
                                    >
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                            <Box style={{ flex: 1 }}>
                                                <Text bold style={{ fontSize: '16px', color: '#333', marginBottom: '4px' }}>
                                                    {item.student?.name}
                                                </Text>
                                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                                    {item.student?.studentId} • {item.class?.name}
                                                </Text>
                                            </Box>
                                            <Text style={{ 
                                                fontSize: '24px', 
                                                fontWeight: 'bold', 
                                                color: getPointsColor(item.currentPoints),
                                                marginLeft: '15px'
                                            }}>
                                                {item.currentPoints}
                                            </Text>
                                        </Box>
                                        
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text style={{ fontSize: '13px', color: '#666' }}>
                                                {ICONS.WARNING} {item.totalViolations || 0} vi phạm • 
                                                {ICONS.POINTS} Điểm ban đầu: {item.initialPoints || 50}
                                            </Text>
                                            
                                            <Box style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                padding: '6px 10px',
                                                borderRadius: '16px',
                                                backgroundColor: `${classificationInfo.color}15`,
                                                border: `1px solid ${classificationInfo.color}40`
                                            }}>
                                                <Text style={{ fontSize: '14px', marginRight: '6px' }}>
                                                    {classificationInfo.icon}
                                                </Text>
                                                <Text style={{ 
                                                    fontSize: '12px', 
                                                    color: classificationInfo.color, 
                                                    fontWeight: 'bold' 
                                                }}>
                                                    {classificationInfo.label}
                                                </Text>
                                            </Box>
                                        </Box>
                                    </Box>
                                );
                            })}
                        </Box>
                    )}
                </Box>
            </Box>

            {/* Adjustment Modal */}
            <Modal
                visible={modalVisible}
                title="Điều chỉnh điểm thi đua"
                onClose={() => {
                    setModalVisible(false);
                    setSelectedStudent(null);
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { 
                        text: submitting ? 'Đang cập nhật...' : 'Cập nhật', 
                        close: false, 
                        onClick: handleAdjustmentSubmit,
                        disabled: submitting || !adjustmentData.points || !adjustmentData.reason.trim()
                    }
                ]}
            >
                {selectedStudent && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px', textAlign: 'center' }}>
                            <Text bold style={{ fontSize: '18px', color: '#0068ff', marginBottom: '5px' }}>
                                {selectedStudent.student?.name}
                            </Text>
                            <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
                                {selectedStudent.student?.studentId} • {selectedStudent.class?.name}
                            </Text>
                            <Text style={{ 
                                fontSize: '24px', 
                                fontWeight: 'bold', 
                                color: getPointsColor(selectedStudent.currentPoints)
                            }}>
                                Điểm hiện tại: {selectedStudent.currentPoints}
                            </Text>
                            <Text style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                                Vi phạm: {selectedStudent.totalViolations || 0} lần • 
                                Điểm ban đầu: {selectedStudent.initialPoints || 50}
                            </Text>
                        </Box>

                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                                Loại điều chỉnh <Text style={{ color: '#dc3545' }}>*</Text>
                            </Text>
                            <Select
                                value={adjustmentData.type}
                                onChange={(value) => setAdjustmentData(prev => ({ ...prev, type: value }))}
                            >
                                <Select.Option key="add" value="add" title="Cộng điểm" />
                                <Select.Option key="deduct" value="deduct" title="Trừ điểm" />
                                <Select.Option key="adjust" value="adjust" title="Điều chỉnh" />
                            </Select>
                        </Box>

                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                                Số điểm <Text style={{ color: '#dc3545' }}>*</Text>
                            </Text>
                            <Input
                                type="number"
                                placeholder="Nhập số điểm..."
                                value={adjustmentData.points}
                                onChange={(e) => setAdjustmentData(prev => ({ ...prev, points: e.target.value }))}
                            />
                        </Box>

                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                                Lý do <Text style={{ color: '#dc3545' }}>*</Text>
                            </Text>
                            <Input.TextArea
                                placeholder="Nhập lý do điều chỉnh (tối thiểu 5 ký tự)..."
                                value={adjustmentData.reason}
                                onChange={(e) => setAdjustmentData(prev => ({ ...prev, reason: e.target.value }))}
                                rows={3}
                            />
                            <Text style={{ 
                                fontSize: '12px', 
                                color: adjustmentData.reason.length < 5 ? '#dc3545' : '#28a745',
                                marginTop: '5px'
                            }}>
                                {adjustmentData.reason.length}/5 ký tự tối thiểu
                            </Text>
                        </Box>

                        {adjustmentData.points && (
                            <Box style={{
                                padding: '12px',
                                backgroundColor: '#f8f9fa',
                                borderRadius: '8px',
                                border: '1px solid #dee2e6'
                            }}>
                                <Text style={{ fontSize: '14px', color: '#666', textAlign: 'center' }}>
                                    <Text bold>Điểm sau điều chỉnh:</Text> {
                                        adjustmentData.type === 'add' 
                                            ? selectedStudent.currentPoints + parseInt(adjustmentData.points || 0)
                                            : adjustmentData.type === 'deduct'
                                            ? selectedStudent.currentPoints - parseInt(adjustmentData.points || 0)
                                            : parseInt(adjustmentData.points || 0)
                                    }
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ConductManagement;
