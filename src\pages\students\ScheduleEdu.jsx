import React, { useEffect, useState } from 'react';
import { Box, Text, Icon, useNavigate } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import Loading from '../../components/utils/Loading';
import { authApi } from '../../utils/api';
import { useSchoolYear } from '../../context/SchoolYearContext';
import HeaderSpacer from '../../components/utils/HeaderSpacer';

const DAYS_OF_WEEK = [
    { name: 'T2', fullName: 'Monday' },
    { name: 'T3', fullName: 'Tuesday' },
    { name: 'T4', fullName: 'Wednesday' },
    { name: 'T5', fullName: 'Thursday' },
    { name: 'T6', fullName: 'Friday' },
    { name: 'T7', fullName: 'Saturday' },
    { name: 'CN', fullName: 'Sunday' },
];

const ScheduleEdu = () => {
    const navigate = useNavigate();
    const { schoolYear } = useSchoolYear(); // Lấy schoolYear từ context
    const [schedules, setSchedules] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [activeDay, setActiveDay] = useState('');

    // Xác định ngày hiện tại để focus tab
    useEffect(() => {
        const today = new Date().getDay();
        const todayIndex = today === 0 ? 6 : today - 1;
        setActiveDay(DAYS_OF_WEEK[todayIndex].fullName);
    }, []);

    // Fetch thời khóa biểu
    useEffect(() => {
        const token = localStorage.getItem('token');
        if (!token) {
            navigate('/login', { replace: true });
            return;
        }
        setLoading(true);
        authApi
            .get('/schedules/my-class')
            .then((response) => {
                setSchedules(response.data);
                setLoading(false);
            })
            .catch((err) => {
                let errorMsg = 'Không thể lấy thời khóa biểu';
                if (err.response?.status === 401) {
                    localStorage.removeItem('token');
                    navigate('/login', { replace: true });
                } else {
                    errorMsg = err.response?.data?.msg || errorMsg;
                }
                setError(errorMsg);
                setLoading(false);
            });
    }, [navigate, schoolYear]);

    // Lọc schedules theo ngày active
    const activeSchedules = schedules.filter((schedule) => schedule.day === activeDay);

    return (
        <Box
            className="container"
            style={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                paddingBottom: '60px',
                backgroundColor: '#f5f5f5',
            }}
        >
            <HeaderEdu title={'Lịch học'} showBackButton={true} />
            <HeaderSpacer />
            {loading && <Loading />}
            {error ? (
                <Box style={{ padding: '16px', textAlign: 'center' }}>
                    <Text>Error: {error}</Text>
                </Box>
            ) : (
                <>
                    {/* Class Info */}
                    <Box
                        style={{
                            backgroundColor: '#0068ff',
                            color: 'white',
                            padding: '8px 16px 16px',
                        }}
                    >
                        <Text bold size="xLarge" style={{ marginBottom: '4px' }}>
                            {schedules[0]?.class?.name || 'Lớp không xác định'}
                        </Text>
                        <Text style={{ fontSize: '14px', opacity: 0.9 }}>
                            Năm học {localStorage.getItem('selectedSchoolYear')}
                        </Text>
                    </Box>

                    {/* Weekday Tabs */}
                    <Box
                        style={{
                            display: 'flex',
                            overflowX: 'auto',
                            backgroundColor: '#f0f3fa',
                            borderBottom: '1px solid #e0e0e0',
                            position: 'sticky',
                            top: 0,
                            zIndex: 10,
                        }}
                    >
                        {DAYS_OF_WEEK.map((day) => (
                            <Box
                                key={day.fullName}
                                style={{
                                    padding: '12px 0',
                                    minWidth: '60px',
                                    textAlign: 'center',
                                    fontSize: '14px',
                                    cursor: 'pointer',
                                    flex: 1,
                                    color: activeDay === day.fullName ? '#0068ff' : '#666',
                                    fontWeight: activeDay === day.fullName ? 'bold' : 'normal',
                                    position: 'relative',
                                }}
                                onClick={() => setActiveDay(day.fullName)}
                            >
                                <Text style={{ marginBottom: '4px' }}>{day.name}</Text>
                                {activeDay === day.fullName && (
                                    <Box
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: '25%',
                                            width: '50%',
                                            height: '3px',
                                            backgroundColor: '#0068ff',
                                            borderRadius: '3px 3px 0 0',
                                        }}
                                    />
                                )}
                            </Box>
                        ))}
                    </Box>

                    {/* Periods */}
                    <Box style={{ padding: '16px' }}>
                        {activeSchedules.length > 0 ? (
                            <>
                                {/* Buổi sáng - Morning periods (1-5) */}
                                <Box style={{ marginBottom: '24px' }}>
                                    <Text
                                        bold
                                        size="large"
                                        style={{
                                            marginBottom: '12px',
                                            color: '#0068ff',
                                            fontSize: '16px',
                                            paddingLeft: '8px',
                                            borderLeft: '3px solid #0068ff'
                                        }}
                                    >
                                        Buổi sáng
                                    </Text>

                                    {activeSchedules[0].periods
                                        .filter(period => period.periodNumber <= 5)
                                        .length > 0 ? (
                                        activeSchedules[0].periods
                                            .filter(period => period.periodNumber <= 5)
                                            .map((period) => (
                                                <Box
                                                    key={period._id}
                                                    style={{
                                                        backgroundColor: 'white',
                                                        borderRadius: '8px',
                                                        boxShadow: '0 2px 6px rgba(0,0,0,0.07)',
                                                        padding: '16px',
                                                        marginBottom: '16px',
                                                        borderLeft: '4px solid #0068ff',
                                                    }}
                                                >
                                                    <Box
                                                        style={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            marginBottom: '8px',
                                                        }}
                                                    >
                                                        <Text
                                                            style={{
                                                                fontWeight: 'bold',
                                                                backgroundColor: '#e6effe',
                                                                color: '#0068ff',
                                                                padding: '4px 8px',
                                                                borderRadius: '4px',
                                                                fontSize: '14px',
                                                            }}
                                                        >
                                                            Tiết {period.periodNumber}
                                                        </Text>
                                                        <Text style={{ color: '#666', fontSize: '14px' }}>
                                                            Phòng {period.room}
                                                        </Text>
                                                    </Box>
                                                    <Text
                                                        style={{
                                                            fontSize: '18px',
                                                            fontWeight: 'bold',
                                                            marginBottom: '4px',
                                                        }}
                                                    >
                                                        {period.subject}
                                                    </Text>
                                                    <Box
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '4px',
                                                            color: '#666',
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        <Icon icon="zi-user" style={{ fontSize: '16px' }} />
                                                        <Text>{period.teacher?.name}</Text>
                                                    </Box>
                                                </Box>
                                            ))
                                    ) : (
                                        <Box
                                            style={{
                                                backgroundColor: 'white',
                                                borderRadius: '8px',
                                                padding: '16px',
                                                textAlign: 'center',
                                                color: '#999',
                                                marginBottom: '16px',
                                            }}
                                        >
                                            <Text>Không có tiết học buổi sáng</Text>
                                        </Box>
                                    )}
                                </Box>

                                {/* Buổi chiều - Afternoon periods (6+) */}
                                <Box>
                                    <Text
                                        bold
                                        size="large"
                                        style={{
                                            marginBottom: '12px',
                                            color: '#FF8C00',
                                            fontSize: '16px',
                                            paddingLeft: '8px',
                                            borderLeft: '3px solid #FF8C00'
                                        }}
                                    >
                                        Buổi chiều
                                    </Text>

                                    {activeSchedules[0].periods
                                        .filter(period => period.periodNumber >= 6)
                                        .length > 0 ? (
                                        activeSchedules[0].periods
                                            .filter(period => period.periodNumber >= 6)
                                            .map((period) => (
                                                <Box
                                                    key={period._id}
                                                    style={{
                                                        backgroundColor: 'white',
                                                        borderRadius: '8px',
                                                        boxShadow: '0 2px 6px rgba(0,0,0,0.07)',
                                                        padding: '16px',
                                                        marginBottom: '16px',
                                                        borderLeft: '4px solid #FF8C00',
                                                    }}
                                                >
                                                    <Box
                                                        style={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            marginBottom: '8px',
                                                        }}
                                                    >
                                                        <Text
                                                            style={{
                                                                fontWeight: 'bold',
                                                                backgroundColor: '#FFF0E0',
                                                                color: '#FF8C00',
                                                                padding: '4px 8px',
                                                                borderRadius: '4px',
                                                                fontSize: '14px',
                                                            }}
                                                        >
                                                            Tiết {period.periodNumber}
                                                        </Text>
                                                        <Text style={{ color: '#666', fontSize: '14px' }}>
                                                            Phòng {period.room}
                                                        </Text>
                                                    </Box>
                                                    <Text
                                                        style={{
                                                            fontSize: '18px',
                                                            fontWeight: 'bold',
                                                            marginBottom: '4px',
                                                        }}
                                                    >
                                                        {period.subject}
                                                    </Text>
                                                    <Box
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '4px',
                                                            color: '#666',
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        <Icon icon="zi-user" style={{ fontSize: '16px' }} />
                                                        <Text>{period.teacher?.name}</Text>
                                                    </Box>
                                                </Box>
                                            ))
                                    ) : (
                                        <Box
                                            style={{
                                                backgroundColor: 'white',
                                                borderRadius: '8px',
                                                padding: '16px',
                                                textAlign: 'center',
                                                color: '#999',
                                            }}
                                        >
                                            <Text>Không có tiết học buổi chiều</Text>
                                        </Box>
                                    )}
                                </Box>
                            </>
                        ) : (
                            <Box
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    padding: '40px 16px',
                                    color: '#999',
                                    textAlign: 'center',
                                    minHeight: '200px',
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: '48px',
                                        marginBottom: '16px',
                                    }}
                                >
                                    {ICONS.SUBJECT}
                                </Text>
                                <Text
                                    bold
                                    style={{
                                        marginBottom: '12px',
                                        fontSize: '16px',
                                        color: '#666',
                                    }}
                                >
                                    Không có tiết học
                                </Text>
                                <Text
                                    style={{
                                        fontSize: '14px',
                                        lineHeight: '1.5',
                                        color: '#666',
                                    }}
                                >
                                    {schedules[0]?.class?.name || 'Lớp'} không có tiết học vào{' '}
                                    {DAYS_OF_WEEK.find((d) => d.fullName === activeDay)?.name}
                                </Text>
                            </Box>
                        )}
                    </Box>
                </>
            )}
            <BottomNavigationEdu />
        </Box>
    );
};

export default ScheduleEdu;